# LearnerPlan Document & Signature Feature

## 🎯 Overview
Added comprehensive document management and signature collection functionality to the LearnerPlan module. This feature allows uploading files or selecting forms with multi-role signature requirements.

## 📋 Feature Components

### **Photo 1: Document Listing with + Sign**
- Displays list of documents for each learner plan
- **+ Sign** in each row opens Photo 3 (signature management)
- Shows document name, type, upload method, and signature status

### **Photo 2: Document Creation Form**
Fields include:
- **Who**: Radio button options
  - `All Aim`
  - `This Aim`
- **File Type**: Dropdown options
  - `ILP File`
  - `General Files` 
  - `Review Files`
  - `Assessment Files`
- **Upload Type**: Radio button options
  - `File Upload` (multiple files supported)
  - `Form Selection` (from Form module)

### **Photo 3: Signature Management**
Opens when clicking + sign from listing, shows:
- Document name and description
- File type information
- List of uploaded files (if file upload type)
- **Signature Roles** with checkboxes:
  - ☐ Primary Assessor
  - ☐ Secondary Assessor  
  - ☐ Learner
  - ☐ Employer

## 🗄️ Database Structure

### **New Entities Created:**

#### **1. LearnerPlanDocument**
```typescript
- document_id: Primary key
- learner_plan: Relation to Learner<PERSON>lan
- name: Document name
- description: Document description
- who: DocumentWho enum (All Aim/This Aim)
- file_type: DocumentFileType enum
- upload_type: DocumentUploadType enum
- uploaded_files: JSON array of file metadata
- selected_form: Relation to Form (optional)
- created_by: User who created the document
- signatures: One-to-many relation to signatures
```

#### **2. LearnerPlanDocumentSignature**
```typescript
- signature_id: Primary key
- document: Relation to LearnerPlanDocument
- role: SignatureRole enum
- is_required: Boolean flag
- status: SignatureStatus enum (Pending/Signed/Declined)
- signed_by: User who signed
- signed_at: Timestamp of signature
- signature_data: Base64 signature or path
- comments: Optional comments
```

## 🔄 API Endpoints

### **1. Create Document**
**POST** `/api/v1/learner-plan-document/create`

#### **File Upload Example:**
```javascript
const formData = new FormData();
formData.append('learner_plan_id', '123');
formData.append('name', 'ILP Assessment Document');
formData.append('description', 'Initial assessment documentation');
formData.append('who', 'This Aim');
formData.append('file_type', 'ILP File');
formData.append('upload_type', 'File Upload');
formData.append('files', file1);
formData.append('files', file2);
formData.append('signature_roles', JSON.stringify(['Primary Assessor', 'Learner']));
```

#### **Form Selection Example:**
```javascript
const formData = new FormData();
formData.append('learner_plan_id', '123');
formData.append('name', 'Training Evaluation');
formData.append('who', 'All Aim');
formData.append('file_type', 'Review Files');
formData.append('upload_type', 'Form Selection');
formData.append('form_id', '456');
formData.append('signature_roles', JSON.stringify(['Primary Assessor', 'Secondary Assessor', 'Employer']));
```

### **2. Get Documents by Learner Plan**
**GET** `/api/v1/learner-plan-document/learner-plan/:learner_plan_id`

#### **Response:**
```json
{
    "message": "Documents fetched successfully",
    "status": true,
    "data": [
        {
            "document_id": 1,
            "name": "ILP Assessment Document",
            "description": "Initial assessment documentation",
            "who": "This Aim",
            "file_type": "ILP File",
            "upload_type": "File Upload",
            "uploaded_files": [
                {
                    "file_name": "assessment.pdf",
                    "file_size": 1024000,
                    "file_url": "https://s3.../LearnerPlanDocument/assessment.pdf",
                    "s3_key": "LearnerPlanDocument/123_assessment.pdf",
                    "uploaded_at": "2024-01-15T10:30:00Z"
                }
            ],
            "signatures": [
                {
                    "signature_id": 1,
                    "role": "Primary Assessor",
                    "is_required": true,
                    "status": "Pending",
                    "signed_by": null,
                    "signed_at": null
                },
                {
                    "signature_id": 2,
                    "role": "Learner",
                    "is_required": true,
                    "status": "Signed",
                    "signed_by": {
                        "user_id": 456,
                        "name": "John Doe"
                    },
                    "signed_at": "2024-01-15T14:30:00Z"
                }
            ]
        }
    ]
}
```

### **3. Get Document Options**
**GET** `/api/v1/learner-plan-document/options`

#### **Response:**
```json
{
    "message": "Document options fetched successfully",
    "status": true,
    "data": {
        "who_options": ["All Aim", "This Aim"],
        "file_types": ["ILP File", "General Files", "Review Files", "Assessment Files"],
        "upload_types": ["File Upload", "Form Selection"],
        "signature_roles": ["Primary Assessor", "Secondary Assessor", "Learner", "Employer"],
        "available_forms": [
            {
                "id": 1,
                "form_name": "Training Evaluation Form",
                "description": "Evaluate training effectiveness",
                "type": "Survey"
            }
        ]
    }
}
```

### **4. Update Signature**
**PATCH** `/api/v1/learner-plan-document/signature/:signature_id`

#### **Request:**
```json
{
    "status": "Signed",
    "signature_data": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "comments": "Approved with minor recommendations"
}
```

### **5. Delete Document**
**DELETE** `/api/v1/learner-plan-document/:document_id`

## 🔧 Frontend Integration Guide

### **Photo 1: Document Listing Component**
```javascript
// Fetch documents for learner plan
const documents = await fetch(`/api/v1/learner-plan-document/learner-plan/${learnerPlanId}`);

// Render table with + sign for signature management
documents.forEach(doc => {
    // Show document info
    // Add + button that opens Photo 3 modal
    const plusButton = `<button onclick="openSignatureModal(${doc.document_id})">+</button>`;
});
```

### **Photo 2: Document Creation Form**
```javascript
// Get options for dropdowns
const options = await fetch('/api/v1/learner-plan-document/options');

// Handle upload type change
const handleUploadTypeChange = (type) => {
    if (type === 'File Upload') {
        showFileUploadSection();
        hideFormSelectionSection();
    } else {
        showFormSelectionSection();
        hideFileUploadSection();
    }
};

// Submit form
const submitDocument = async (formData) => {
    const response = await fetch('/api/v1/learner-plan-document/create', {
        method: 'POST',
        body: formData
    });
};
```

### **Photo 3: Signature Management Modal**
```javascript
// Open signature modal
const openSignatureModal = async (documentId) => {
    const document = await fetch(`/api/v1/learner-plan-document/learner-plan/${learnerPlanId}`);
    
    // Display document details
    // Show uploaded files list
    // Render signature role checkboxes with status
    
    document.signatures.forEach(signature => {
        const checkbox = `
            <input type="checkbox" 
                   ${signature.status === 'Signed' ? 'checked disabled' : ''} 
                   onchange="updateSignature(${signature.signature_id})">
            ${signature.role} - ${signature.status}
        `;
    });
};

// Update signature
const updateSignature = async (signatureId, signatureData) => {
    await fetch(`/api/v1/learner-plan-document/signature/${signatureId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            status: 'Signed',
            signature_data: signatureData,
            comments: 'Approved'
        })
    });
};
```

## 🗄️ Database Migration

**File**: `1750800000000-CreateLearnerPlanDocumentEntities.ts`

**Run Migration:**
```bash
npm run migration:run
```

## ✅ Key Features

1. **Dual Upload Options**: File upload or form selection
2. **Multi-Role Signatures**: Support for 4 different signature roles
3. **File Management**: Multiple file upload with S3 storage
4. **Form Integration**: Seamless integration with existing Form module
5. **Status Tracking**: Track signature status for each role
6. **Flexible Assignment**: Support for "All Aim" vs "This Aim" targeting
7. **Rich Metadata**: Store file information and signature details

This implementation provides a comprehensive document and signature management system that integrates seamlessly with the existing LearnerPlan module while offering flexible file handling and multi-role signature collection capabilities.
