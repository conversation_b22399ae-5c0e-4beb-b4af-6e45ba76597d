import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateLearnerPlanDocumentEntities1750800000000 implements MigrationInterface {
    name = 'CreateLearnerPlanDocumentEntities1750800000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create learner_plan_document table
        await queryRunner.query(`
            CREATE TABLE "learner_plan_document" (
                "document_id" SERIAL NOT NULL,
                "name" character varying NOT NULL,
                "description" text,
                "who" character varying NOT NULL DEFAULT 'This Aim',
                "file_type" character varying NOT NULL DEFAULT 'General Files',
                "upload_type" character varying NOT NULL DEFAULT 'File Upload',
                "uploaded_files" json,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "learner_plan_id" integer,
                "form_id" integer,
                "created_by" integer,
                CONSTRAINT "PK_learner_plan_document" PRIMARY KEY ("document_id")
            )
        `);

        // Create learner_plan_document_signature table
        await queryRunner.query(`
            CREATE TABLE "learner_plan_document_signature" (
                "signature_id" SERIAL NOT NULL,
                "role" character varying NOT NULL,
                "is_required" boolean NOT NULL DEFAULT true,
                "status" character varying NOT NULL DEFAULT 'Pending',
                "signed_at" TIMESTAMP,
                "signature_data" text,
                "comments" text,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "document_id" integer,
                "signed_by" integer,
                CONSTRAINT "PK_learner_plan_document_signature" PRIMARY KEY ("signature_id")
            )
        `);

        // Add foreign key constraints for learner_plan_document
        await queryRunner.query(`
            ALTER TABLE "learner_plan_document" 
            ADD CONSTRAINT "FK_learner_plan_document_learner_plan" 
            FOREIGN KEY ("learner_plan_id") 
            REFERENCES "learner_plan"("learner_plan_id") 
            ON DELETE CASCADE ON UPDATE NO ACTION
        `);

        await queryRunner.query(`
            ALTER TABLE "learner_plan_document" 
            ADD CONSTRAINT "FK_learner_plan_document_form" 
            FOREIGN KEY ("form_id") 
            REFERENCES "form"("id") 
            ON DELETE SET NULL ON UPDATE NO ACTION
        `);

        await queryRunner.query(`
            ALTER TABLE "learner_plan_document" 
            ADD CONSTRAINT "FK_learner_plan_document_created_by" 
            FOREIGN KEY ("created_by") 
            REFERENCES "user"("user_id") 
            ON DELETE SET NULL ON UPDATE NO ACTION
        `);

        // Add foreign key constraints for learner_plan_document_signature
        await queryRunner.query(`
            ALTER TABLE "learner_plan_document_signature" 
            ADD CONSTRAINT "FK_learner_plan_document_signature_document" 
            FOREIGN KEY ("document_id") 
            REFERENCES "learner_plan_document"("document_id") 
            ON DELETE CASCADE ON UPDATE NO ACTION
        `);

        await queryRunner.query(`
            ALTER TABLE "learner_plan_document_signature" 
            ADD CONSTRAINT "FK_learner_plan_document_signature_signed_by" 
            FOREIGN KEY ("signed_by") 
            REFERENCES "user"("user_id") 
            ON DELETE SET NULL ON UPDATE NO ACTION
        `);

        // Create indexes for better performance
        await queryRunner.query(`
            CREATE INDEX "IDX_learner_plan_document_learner_plan_id" 
            ON "learner_plan_document" ("learner_plan_id")
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_learner_plan_document_signature_document_id" 
            ON "learner_plan_document_signature" ("document_id")
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_learner_plan_document_signature_role" 
            ON "learner_plan_document_signature" ("role")
        `);

        console.log('✅ Created learner_plan_document and learner_plan_document_signature tables');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraints first
        await queryRunner.query(`ALTER TABLE "learner_plan_document_signature" DROP CONSTRAINT "FK_learner_plan_document_signature_signed_by"`);
        await queryRunner.query(`ALTER TABLE "learner_plan_document_signature" DROP CONSTRAINT "FK_learner_plan_document_signature_document"`);
        await queryRunner.query(`ALTER TABLE "learner_plan_document" DROP CONSTRAINT "FK_learner_plan_document_created_by"`);
        await queryRunner.query(`ALTER TABLE "learner_plan_document" DROP CONSTRAINT "FK_learner_plan_document_form"`);
        await queryRunner.query(`ALTER TABLE "learner_plan_document" DROP CONSTRAINT "FK_learner_plan_document_learner_plan"`);

        // Drop indexes
        await queryRunner.query(`DROP INDEX "IDX_learner_plan_document_signature_role"`);
        await queryRunner.query(`DROP INDEX "IDX_learner_plan_document_signature_document_id"`);
        await queryRunner.query(`DROP INDEX "IDX_learner_plan_document_learner_plan_id"`);

        // Drop tables
        await queryRunner.query(`DROP TABLE "learner_plan_document_signature"`);
        await queryRunner.query(`DROP TABLE "learner_plan_document"`);

        console.log('✅ Dropped learner_plan_document and learner_plan_document_signature tables');
    }
}
