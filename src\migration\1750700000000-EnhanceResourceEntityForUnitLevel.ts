import { MigrationInterface, QueryRunner } from "typeorm";

export class EnhanceResourceEntityForUnitLevel1750700000000 implements MigrationInterface {
    name = 'EnhanceResourceEntityForUnitLevel1750700000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add resource_level column
        const hasResourceLevelColumn = await queryRunner.hasColumn("resource", "resource_level");
        if (!hasResourceLevelColumn) {
            await queryRunner.query(`
                ALTER TABLE "resource" 
                ADD "resource_level" character varying DEFAULT 'Course'
            `);
            console.log('✅ Added resource_level column to resource table');
        } else {
            console.log('ℹ️ resource_level column already exists in resource table');
        }

        // Add unit_info column
        const hasUnitInfoColumn = await queryRunner.hasColumn("resource", "unit_info");
        if (!hasUnitInfoColumn) {
            await queryRunner.query(`
                ALTER TABLE "resource" 
                ADD "unit_info" json
            `);
            console.log('✅ Added unit_info column to resource table');
        } else {
            console.log('ℹ️ unit_info column already exists in resource table');
        }

        // Add files column for multiple file support
        const hasFilesColumn = await queryRunner.hasColumn("resource", "files");
        if (!hasFilesColumn) {
            await queryRunner.query(`
                ALTER TABLE "resource" 
                ADD "files" json
            `);
            console.log('✅ Added files column to resource table');
        } else {
            console.log('ℹ️ files column already exists in resource table');
        }

        // Add external_url column
        const hasExternalUrlColumn = await queryRunner.hasColumn("resource", "external_url");
        if (!hasExternalUrlColumn) {
            await queryRunner.query(`
                ALTER TABLE "resource" 
                ADD "external_url" character varying
            `);
            console.log('✅ Added external_url column to resource table');
        } else {
            console.log('ℹ️ external_url column already exists in resource table');
        }

        // Make size column nullable
        const sizeColumn = await queryRunner.getTable("resource");
        const sizeColumnDef = sizeColumn?.findColumnByName("size");
        if (sizeColumnDef && !sizeColumnDef.isNullable) {
            await queryRunner.query(`
                ALTER TABLE "resource" 
                ALTER COLUMN "size" DROP NOT NULL
            `);
            console.log('✅ Made size column nullable in resource table');
        } else {
            console.log('ℹ️ size column is already nullable in resource table');
        }

        // Make url column nullable
        const urlColumn = await queryRunner.getTable("resource");
        const urlColumnDef = urlColumn?.findColumnByName("url");
        if (urlColumnDef && !urlColumnDef.isNullable) {
            await queryRunner.query(`
                ALTER TABLE "resource" 
                ALTER COLUMN "url" DROP NOT NULL
            `);
            console.log('✅ Made url column nullable in resource table');
        } else {
            console.log('ℹ️ url column is already nullable in resource table');
        }

        // Update existing records to have Course level
        await queryRunner.query(`
            UPDATE "resource" 
            SET "resource_level" = 'Course' 
            WHERE "resource_level" IS NULL
        `);

        console.log('✅ Set default resource_level for existing records');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove resource_level column
        const hasResourceLevelColumn = await queryRunner.hasColumn("resource", "resource_level");
        if (hasResourceLevelColumn) {
            await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "resource_level"`);
            console.log('✅ Removed resource_level column from resource table');
        }

        // Remove unit_info column
        const hasUnitInfoColumn = await queryRunner.hasColumn("resource", "unit_info");
        if (hasUnitInfoColumn) {
            await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "unit_info"`);
            console.log('✅ Removed unit_info column from resource table');
        }

        // Remove files column
        const hasFilesColumn = await queryRunner.hasColumn("resource", "files");
        if (hasFilesColumn) {
            await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "files"`);
            console.log('✅ Removed files column from resource table');
        }

        // Remove external_url column
        const hasExternalUrlColumn = await queryRunner.hasColumn("resource", "external_url");
        if (hasExternalUrlColumn) {
            await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "external_url"`);
            console.log('✅ Removed external_url column from resource table');
        }

        // Make size column not nullable again (if needed)
        await queryRunner.query(`
            ALTER TABLE "resource" 
            ALTER COLUMN "size" SET NOT NULL
        `);

        // Make url column not nullable again (if needed)
        await queryRunner.query(`
            ALTER TABLE "resource" 
            ALTER COLUMN "url" SET NOT NULL
        `);

        console.log('✅ Reverted resource table changes');
    }
}
