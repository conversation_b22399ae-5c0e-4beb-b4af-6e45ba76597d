import { Router } from 'express';
import { LearnerPlanDocumentController } from '../controllers/LearnerPlanDocumentController';
import { authorizeRoles } from '../middleware/verifyToken';
import { multipleFileUpload } from '../util/multer';

const router = Router();
const learnerPlanDocumentController = new LearnerPlanDocumentController();

// Create document with files or form selection
router.post('/create', 
    authorizeRoles(), 
    multipleFileUpload("files", 10), 
    learnerPlanDocumentController.createDocument
);

// Get documents by learner plan ID
router.get('/learner-plan/:learner_plan_id', 
    authorizeRoles(), 
    learnerPlanDocumentController.getDocumentsByLearnerPlan
);

// Get document creation options (enums and forms)
router.get('/options', 
    authorizeRoles(), 
    learnerPlanDocumentController.getDocumentOptions
);

// Update signature status
router.patch('/signature/:signature_id', 
    authorizeRoles(), 
    learnerPlanDocumentController.updateSignature
);

// Delete document
router.delete('/:document_id', 
    authorizeRoles(), 
    learnerPlanDocumentController.deleteDocument
);

export default router;
