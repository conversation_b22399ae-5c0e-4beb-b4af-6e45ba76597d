# Form Assignment Email API

## Overview
Added email functionality to send notifications when dynamic forms are created and assigned to users. The API supports sending emails to individual users, specific roles, or all users with optional PDF attachments.

## 🆕 New Feature

### **Send Form Assignment Email**
**Endpoint**: `POST /api/v1/form/send-assignment-email`

This API sends email notifications to assigned users when forms are created and assigned. It supports:
- Individual user targeting
- Role-based targeting (All Learner, All Trainer, etc.)
- PDF attachment support
- Custom email subject and message
- Batch email processing with status tracking

## 📋 API Details

### **Request Parameters**

#### **Form Data Fields:**
```
form_id: number (required) - ID of the form to send notifications for
user_ids: number[] (optional) - Array of specific user IDs to send emails to
assign: string (optional) - Role-based assignment ("All", "All Learner", "All Trainer", etc.)
email_subject: string (optional) - Custom email subject line
email_message: string (optional) - Additional message to include in email
pdf: File (optional) - PDF file to attach to the email
```

#### **Assignment Options:**
- `user_ids`: Send to specific users by ID
- `assign`: Send to users by role:
  - `"All"` - All users (except Admin)
  - `"All Learner"` - All learners
  - `"All Trainer"` - All trainers
  - `"All Employer"` - All employers
  - `"All IQA"` - All IQA users
  - `"All LIQA"` - All LIQA users
  - `"All EQA"` - All EQA users
- If neither provided: Send to all users currently assigned to the form

### **Request Examples**

#### **1. Send to Specific Users with PDF**
```javascript
const formData = new FormData();
formData.append('form_id', '123');
formData.append('user_ids', JSON.stringify([1, 2, 3]));
formData.append('email_subject', 'New Training Form Available');
formData.append('email_message', 'Please complete this form by Friday.');
formData.append('pdf', pdfFile);

fetch('/api/v1/form/send-assignment-email', {
    method: 'POST',
    body: formData
});
```

#### **2. Send to All Learners**
```javascript
const formData = new FormData();
formData.append('form_id', '123');
formData.append('assign', 'All Learner');
formData.append('email_subject', 'Mandatory Training Form');

fetch('/api/v1/form/send-assignment-email', {
    method: 'POST',
    body: formData
});
```

#### **3. Send to All Users Assigned to Form**
```javascript
const formData = new FormData();
formData.append('form_id', '123');
formData.append('email_message', 'This form is now available for completion.');

fetch('/api/v1/form/send-assignment-email', {
    method: 'POST',
    body: formData
});
```

### **Response Format**

#### **Success Response:**
```json
{
    "message": "Form assignment emails processed. 5 sent successfully, 0 failed.",
    "status": true,
    "data": {
        "form_id": 123,
        "form_name": "Training Evaluation Form",
        "total_recipients": 5,
        "successful_sends": 5,
        "failed_sends": 0,
        "email_results": [
            {
                "user_id": 1,
                "email": "<EMAIL>",
                "status": "sent"
            },
            {
                "user_id": 2,
                "email": "<EMAIL>",
                "status": "sent"
            }
        ],
        "pdf_attached": true
    }
}
```

#### **Error Response:**
```json
{
    "message": "Form not found",
    "status": false
}
```

## 📧 Email Template

### **Default Email Content:**
The system generates a professional HTML email template that includes:

- **Header**: Form assignment notification
- **Form Details**: Form name and description
- **Custom Message**: Any additional message provided
- **Action Button**: Link to access the form
- **Footer**: System information and disclaimer

### **Email Template Structure:**
```html
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2 style="color: #333;">Form Assignment Notification</h2>
    <p>You have been assigned a new form to complete:</p>
    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin: 0; color: #2c3e50;">[FORM_NAME]</h3>
        <p style="margin: 10px 0 0 0; color: #666;">[FORM_DESCRIPTION]</p>
    </div>
    <p>[CUSTOM_MESSAGE]</p>
    <p>Please log in to the system to access and complete the form.</p>
    <p style="margin-top: 30px;">
        <a href="[FRONTEND_URL]" style="background-color: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Access Form</a>
    </p>
    <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
    <p style="font-size: 12px; color: #666;">
        This is an automated message from the Locker system. Please do not reply to this email.
    </p>
</div>
```

## 🔧 Technical Implementation

### **PDF Attachment Handling:**
- PDF files are uploaded to S3 under "FormPDF" folder
- Attachment includes original filename and proper content type
- Files are accessible via secure S3 URLs

### **Email Processing:**
- Batch processing with Promise.all for concurrent sending
- Individual error handling per recipient
- Detailed status tracking for each email attempt

### **User Targeting Logic:**
1. **Specific Users**: Query by user IDs
2. **Role-Based**: Query by user roles using PostgreSQL array operations
3. **Form Assigned**: Use existing form-user relationships

### **Error Handling:**
- Form validation (existence check)
- User validation (target users exist)
- Individual email failure tracking
- Comprehensive error reporting

## 🔐 Security & Authorization

### **Access Control:**
- Requires Admin or Trainer role
- Form access validation
- User data protection

### **File Upload Security:**
- PDF file type validation
- S3 secure storage
- Temporary URL generation

## 📊 Usage Scenarios

### **1. Course Assignment Workflow:**
```javascript
// 1. Create form
const form = await createForm({
    form_name: "Course Evaluation",
    description: "Please evaluate the training course",
    form_data: {...}
});

// 2. Assign to users
await assignUsersToForm(form.id, {
    assign: "All Learner"
});

// 3. Send notification emails with PDF
await sendFormAssignmentEmail({
    form_id: form.id,
    assign: "All Learner",
    email_subject: "Course Evaluation Form",
    email_message: "Please complete by end of week",
    pdf: evaluationGuidePDF
});
```

### **2. Individual Assignment:**
```javascript
// Send to specific users with custom message
await sendFormAssignmentEmail({
    form_id: 456,
    user_ids: [1, 2, 3],
    email_subject: "Personal Development Plan",
    email_message: "This is your personalized development form.",
    pdf: pdpTemplatePDF
});
```

### **3. Role-Based Distribution:**
```javascript
// Send to all trainers
await sendFormAssignmentEmail({
    form_id: 789,
    assign: "All Trainer",
    email_subject: "Monthly Training Report",
    pdf: reportTemplatePDF
});
```

## ✅ Testing Checklist

### **Functionality Testing:**
- [ ] Send to individual users
- [ ] Send to role-based groups
- [ ] Send to form-assigned users
- [ ] PDF attachment handling
- [ ] Custom subject and message
- [ ] Email delivery confirmation

### **Error Handling Testing:**
- [ ] Invalid form ID
- [ ] No target users found
- [ ] Email sending failures
- [ ] PDF upload errors
- [ ] Authorization failures

### **Integration Testing:**
- [ ] Form creation → assignment → email workflow
- [ ] Multiple recipient handling
- [ ] S3 PDF storage and retrieval
- [ ] Email template rendering

## 🚀 Benefits

1. **Automated Notifications**: Streamlined form assignment process
2. **Flexible Targeting**: Support for individual and group assignments
3. **Rich Content**: HTML emails with PDF attachments
4. **Status Tracking**: Detailed delivery reporting
5. **Professional Presentation**: Branded email templates
6. **Scalable**: Handles bulk email distribution efficiently

This implementation provides a comprehensive solution for form assignment notifications with professional email delivery and robust error handling.
