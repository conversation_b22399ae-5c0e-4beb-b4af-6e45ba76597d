import { MigrationInterface, QueryRunner } from "typeorm";

export class AddStatusAndWhoFieldsToSessionLearnerAction1750600000000 implements MigrationInterface {
    name = 'AddStatusAndWhoFieldsToSessionLearnerAction1750600000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if learner_status column exists
        const hasLearnerStatusColumn = await queryRunner.hasColumn("session_learner_action", "learner_status");
        
        if (!hasLearnerStatusColumn) {
            await queryRunner.query(`
                ALTER TABLE "session_learner_action" 
                ADD "learner_status" character varying
            `);
            console.log('✅ Added learner_status column to session_learner_action table');
        } else {
            console.log('ℹ️ learner_status column already exists in session_learner_action table');
        }

        // Check if trainer_status column exists
        const hasTrainerStatusColumn = await queryRunner.hasColumn("session_learner_action", "trainer_status");
        
        if (!hasTrainerStatusColumn) {
            await queryRunner.query(`
                ALTER TABLE "session_learner_action" 
                ADD "trainer_status" character varying
            `);
            console.log('✅ Added trainer_status column to session_learner_action table');
        } else {
            console.log('ℹ️ trainer_status column already exists in session_learner_action table');
        }

        // Check if who column exists
        const hasWhoColumn = await queryRunner.hasColumn("session_learner_action", "who");
        
        if (!hasWhoColumn) {
            await queryRunner.query(`
                ALTER TABLE "session_learner_action" 
                ADD "who" character varying
            `);
            console.log('✅ Added who column to session_learner_action table');
        } else {
            console.log('ℹ️ who column already exists in session_learner_action table');
        }

        // Set default values for existing records
        await queryRunner.query(`
            UPDATE "session_learner_action" 
            SET "learner_status" = 'not started' 
            WHERE "learner_status" IS NULL
        `);

        await queryRunner.query(`
            UPDATE "session_learner_action" 
            SET "trainer_status" = 'not started' 
            WHERE "trainer_status" IS NULL
        `);

        console.log('✅ Set default values for existing records');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove learner_status column
        const hasLearnerStatusColumn = await queryRunner.hasColumn("session_learner_action", "learner_status");
        
        if (hasLearnerStatusColumn) {
            await queryRunner.query(`ALTER TABLE "session_learner_action" DROP COLUMN "learner_status"`);
            console.log('✅ Removed learner_status column from session_learner_action table');
        }

        // Remove trainer_status column
        const hasTrainerStatusColumn = await queryRunner.hasColumn("session_learner_action", "trainer_status");
        
        if (hasTrainerStatusColumn) {
            await queryRunner.query(`ALTER TABLE "session_learner_action" DROP COLUMN "trainer_status"`);
            console.log('✅ Removed trainer_status column from session_learner_action table');
        }

        // Remove who column
        const hasWhoColumn = await queryRunner.hasColumn("session_learner_action", "who");
        
        if (hasWhoColumn) {
            await queryRunner.query(`ALTER TABLE "session_learner_action" DROP COLUMN "who"`);
            console.log('✅ Removed who column from session_learner_action table');
        }
    }
}
